import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import '../lib/models/payment_gateway.dart';
import '../lib/models/wallet/wallet_response.dart';

void main() {
  group('Dynamic Payment Gateway Tests', () {
    test('PaymentGateway model should parse from JSON correctly', () {
      final json = {
        'id': 'phonepe',
        'name': 'phonepe',
        'display_name': 'PhonePe',
        'subtitle': 'Fast & secure payments',
        'is_enabled': true,
        'icon_name': 'phone_android',
        'color': '#5F259F',
        'priority': 1,
      };

      final gateway = PaymentGateway.fromJson(json);

      expect(gateway.id, equals('phonepe'));
      expect(gateway.name, equals('phonepe'));
      expect(gateway.displayName, equals('PhonePe'));
      expect(gateway.subtitle, equals('Fast & secure payments'));
      expect(gateway.isEnabled, equals(true));
      expect(gateway.iconName, equals('phone_android'));
      expect(gateway.colorHex, equals('#5F259F'));
      expect(gateway.priority, equals(1));
    });

    test('PaymentGateway should provide correct icon', () {
      final phonePeGateway = PaymentGateway(
        id: 'phonepe',
        name: 'phonepe',
        displayName: 'PhonePe',
        subtitle: 'Fast & secure payments',
        iconName: 'phone_android',
      );

      final payUGateway = PaymentGateway(
        id: 'payu',
        name: 'payu',
        displayName: 'PayU',
        subtitle: 'Secure online payments',
        iconName: 'payment',
      );

      expect(phonePeGateway.getIcon(), equals(Icons.phone_android));
      expect(payUGateway.getIcon(), equals(Icons.payment));
    });

    test('PaymentGateway should provide correct color', () {
      final gateway = PaymentGateway(
        id: 'phonepe',
        name: 'phonepe',
        displayName: 'PhonePe',
        subtitle: 'Fast & secure payments',
        colorHex: '#5F259F',
      );

      final color = gateway.getColor();
      expect(color.value, equals(0xFF5F259F));
    });

    test('PaymentGateway should fallback to default color for invalid hex', () {
      final gateway = PaymentGateway(
        id: 'phonepe',
        name: 'phonepe',
        displayName: 'PhonePe',
        subtitle: 'Fast & secure payments',
        colorHex: 'invalid_color',
      );

      final color = gateway.getColor();
      expect(color.value, equals(0xFF5F259F)); // Should fallback to PhonePe default
    });

    test('PaymentGateway should provide default gateways', () {
      final defaultGateways = PaymentGateway.getDefaultGateways();

      expect(defaultGateways.length, equals(2)); // PhonePe and PayU
      expect(defaultGateways[0].id, equals('phonepe'));
      expect(defaultGateways[1].id, equals('payu'));
    });

    test('WalletResponse should parse payment gateways from JSON', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'phonepe',
        'payment_gateways': [
          {
            'id': 'phonepe',
            'name': 'phonepe',
            'display_name': 'PhonePe',
            'subtitle': 'Fast & secure payments',
            'is_enabled': true,
            'icon_name': 'phone_android',
            'color': '#5F259F',
            'priority': 1,
          },
          {
            'id': 'payu',
            'name': 'payu',
            'display_name': 'PayU',
            'subtitle': 'Secure online payments',
            'is_enabled': true,
            'icon_name': 'payment',
            'color': '#00A651',
            'priority': 2,
          },
        ],
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);

      expect(walletResponse.paymentGateways, isNotNull);
      expect(walletResponse.paymentGateways!.length, equals(2));
      expect(walletResponse.paymentGateways![0].id, equals('phonepe'));
      expect(walletResponse.paymentGateways![1].id, equals('payu'));
    });

    test('WalletResponse should use default gateways when none provided', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'phonepe',
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);

      expect(walletResponse.paymentGateways, isNotNull);
      expect(walletResponse.paymentGateways!.length, equals(2)); // Default gateways
      expect(walletResponse.paymentGateways![0].id, equals('phonepe'));
      expect(walletResponse.paymentGateways![1].id, equals('payu'));
    });

    test('WalletResponse should filter enabled gateways correctly', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'phonepe',
        'payment_gateways': [
          {
            'id': 'phonepe',
            'name': 'phonepe',
            'display_name': 'PhonePe',
            'subtitle': 'Fast & secure payments',
            'is_enabled': true,
            'priority': 1,
          },
          {
            'id': 'payu',
            'name': 'payu',
            'display_name': 'PayU',
            'subtitle': 'Secure online payments',
            'is_enabled': false, // Disabled
            'priority': 2,
          },
          {
            'id': 'cashfree',
            'name': 'cashfree',
            'display_name': 'Cashfree',
            'subtitle': 'Fast & reliable payments',
            'is_enabled': true,
            'priority': 3,
          },
        ],
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);
      final enabledGateways = walletResponse.getEnabledGateways();

      expect(enabledGateways.length, equals(2)); // Only enabled ones
      expect(enabledGateways[0].id, equals('phonepe')); // Priority 1
      expect(enabledGateways[1].id, equals('cashfree')); // Priority 3
    });

    test('WalletResponse should sort gateways by priority', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'phonepe',
        'payment_gateways': [
          {
            'id': 'cashfree',
            'name': 'cashfree',
            'display_name': 'Cashfree',
            'subtitle': 'Fast & reliable payments',
            'is_enabled': true,
            'priority': 3,
          },
          {
            'id': 'phonepe',
            'name': 'phonepe',
            'display_name': 'PhonePe',
            'subtitle': 'Fast & secure payments',
            'is_enabled': true,
            'priority': 1,
          },
          {
            'id': 'payu',
            'name': 'payu',
            'display_name': 'PayU',
            'subtitle': 'Secure online payments',
            'is_enabled': true,
            'priority': 2,
          },
        ],
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);
      final enabledGateways = walletResponse.getEnabledGateways();

      expect(enabledGateways.length, equals(3));
      expect(enabledGateways[0].id, equals('phonepe')); // Priority 1
      expect(enabledGateways[1].id, equals('payu')); // Priority 2
      expect(enabledGateways[2].id, equals('cashfree')); // Priority 3
    });
  });
}
